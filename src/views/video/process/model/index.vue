<template>
  <div class="algorithm-model-page">
    <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main hidden-sheet-table">
      <!-- 工具栏自定义按钮 -->
      <template #toolbar:after>
      </template>

      <!-- 自定义卡片布局区域 -->
      <template #table-before>
        <div class="card-container">
          <div v-if="tableMixin.loading" class="loading-container">
            <el-loading :loading="true" text="加载中..." />
          </div>
          <div v-else-if="tableMixin.data.length === 0" class="empty-container">
            <el-empty description="暂无数据" />
          </div>
          <div v-else class="card-grid">
            <div
              v-for="(item, index) in tableMixin.data"
              :key="item.id || index"
              class="algorithm-card"
              @click="handleCardClick(item)"
            >
              <el-card shadow="hover" class="card-item">
                <!-- 卡片头部 -->
                <div class="card-header">
                  <div class="algorithm-info">
                    <div class="algorithm-icon">
                      <i :class="getAlgorithmIcon(item.value3)" />
                    </div>
                    <div class="algorithm-details">
                      <h3 class="algorithm-name">{{ item.value2 }}</h3>
                      <p class="algorithm-code">{{ item.value1 }}</p>
                    </div>
                  </div>
                  <div class="algorithm-status">
                    <el-tag
                      :type="getStatusType(item.value6)"
                      size="small"
                    >
                      {{ getStatusLabel(item.value6) }}
                    </el-tag>
                  </div>
                </div>

                <!-- 卡片内容 -->
                <div class="card-content">
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="info-label">算法类型</span>
                      <span class="info-value">{{ getTypeLabel(item.value3) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">业务类型</span>
                      <span class="info-value">{{ getBusinessTypeLabel(item.value9) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">使用次数</span>
                      <span class="info-value">{{ parseInt(item.value10) || 0 }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">参数量</span>
                      <span class="info-value">{{ parseInt(item.value4) || 0 }}</span>
                    </div>
                  </div>
                </div>

                <!-- 卡片操作 -->
                <div class="card-actions">
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-setting"
                    @click.stop="handleParams(item)"
                  >
                    参数
                  </el-button>
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-edit"
                    @click.stop="handleEdit(item)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    :type="item.value6 === 'enabled' ? 'text' : 'text'"
                    size="mini"
                    :icon="item.value6 === 'enabled' ? 'el-icon-video-pause' : 'el-icon-video-play'"
                    @click.stop="handleToggleStatus(item)"
                  >
                    {{ item.value6 === '启用' ? '禁用' : '启用' }}
                  </el-button>
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-delete"
                    class="danger-btn"
                    @click.stop="handleDelete(item)"
                  >
                    删除
                  </el-button>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </template>

    </EleSheet>

    <!-- 模型参数配置对话框 -->
    <ModelParamsDialog
      ref="modelParamsDialog"
      @success="handleParamsSuccess"
    />
  </div>
</template>

<script>
import { algorithmType, algorithmStatus, businessType } from '@/dicts/video/index.js'
import request from '@/utils/request.js'
import ModelParamsDialog from './components/ModelParamsDialog.vue'

export default {
  name: 'AlgorithmModelPage',
  components: {
    ModelParamsDialog
  },
  data() {
    return {
      tableType: 'ai_algorithm_model_management',
      // 字典数据
      algorithmTypeDict: algorithmType,
      algorithmStatusDict: algorithmStatus,
      businessTypeDict: businessType,
      sheetRef: null
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '算法模型管理',
        layout: 'toolbar,table,paging',
        tableProps: {
          hidden: true
        },
        defaultPageSize: 12,
        pagingProps: {
          pageSizes: [
            12,
            24,
            48,
            96
          ]
        },
        api: {
          list: async(params) => {
            return await request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: this.tableType
              }
            })
          },
          add: async(params) => {
            return await request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...params,
                type: this.tableType
              }
            })
          },
          edit: async(params) => {
            return await request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...params,
                type: this.tableType
              }
            })
          },
          info: async(id) => {
            return await request({
              url: `/system/AutoOsmotic/${id}`
            })
          },
          remove: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'delete'
            })
          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: this.tableType
          //     })
          //   })
        },
        model: {
          id: {
            type: 'text',
            label: 'ID',
            width: 80,
            fixed: 'left',
            search: { hidden: true },
            form: { hidden: true },
            info: { hidden: true }
          },
          value1: {
            type: 'text',
            label: '算法编码',
            width: 120,
            search: {
              type: 'input',
              placeholder: '请输入算法编码'
            },
            form: {
              type: 'input',
              placeholder: '请输入算法编码',
              rules: [
                { required: true, message: '算法编码不能为空', trigger: 'blur' }
                // { min: 3, max: 20, message: '算法编码长度必须介于 3 和 20 之间', trigger: 'blur' },
                // { pattern: /^[A-Z][A-Z0-9_]*$/, message: '算法编码必须以大写字母开头，只能包含大写字母、数字和下划线', trigger: 'blur' }
              ]
            }
          },
          value2: {
            type: 'text',
            label: '算法名称',
            width: 200,
            showOverflowTooltip: true,
            search: {
              type: 'input',
              placeholder: '请输入算法名称'
            },
            form: {
              type: 'input',
              placeholder: '请输入算法名称',
              rules: [
                { required: true, message: '算法名称不能为空', trigger: 'blur' }
                // { min: 2, max: 50, message: '算法名称长度必须介于 2 和 50 之间', trigger: 'blur' }
              ]
            }
          },
          value5: {
            type: 'text',
            label: '版本号',
            width: 100,
            search: { hidden: true },
            form: {
              type: 'input',
              placeholder: '请输入版本号'
              // rules: [
              //   { required: true, message: '版本号不能为空', trigger: 'blur' },
              //   { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，应为 x.x.x 格式', trigger: 'blur' }
              // ]
            }
          },
          value6: {
            type: 'text',
            label: '状态',
            width: 100,
            search: {
              type: 'select',
              placeholder: '请选择状态',
              clearable: true,
              options: [
                ...this.algorithmStatusDict
              ]
            },
            form: {
              type: 'select',
              placeholder: '请选择状态',
              options: this.algorithmStatusDict,
              rules: [
                { required: true, message: '请选择状态', trigger: 'change' }
              ],
              value: '启用'
            }
          },
          value3: {
            type: 'text',
            label: '算法类型',
            width: 120,
            search: {
              type: 'select',
              placeholder: '请选择算法类型',
              clearable: true,
              options: [
                ...this.algorithmTypeDict
              ]
            },
            form: {
              type: 'select',
              placeholder: '请选择算法类型',
              options: this.algorithmTypeDict,
              rules: [
                { required: true, message: '请选择算法类型', trigger: 'change' }
              ]
            }
          },
          value4: {
            type: 'text',
            label: '参数量',
            width: 100,
            search: { hidden: true },
            form: {
              placeholder: '请输入参数量',
              props: {
                min: 0,
                max: 100
              },
              rules: [
                { required: true, message: '参数量不能为空', trigger: 'blur' }
              ]
            }
          },

          value8: {
            type: 'text',
            label: '修改时间',
            width: 160,
            search: { hidden: true },
            form: {
              hidden: true,
              type: 'date-picker',
              props: {
                type: 'datetime',
                placeholder: '请选择修改时间',
                format: 'yyyy-MM-dd HH:mm:ss',
                valueFormat: 'yyyy-MM-dd HH:mm:ss',
                disabled: true
              }
            }
          },
          value9: {
            type: 'text',
            label: '业务类型',
            width: 120,
            search: {
              type: 'select',
              placeholder: '请选择业务类型',
              clearable: true,
              options: [
                ...this.businessTypeDict
              ]
            },
            form: {
              type: 'select',
              placeholder: '请选择业务类型',
              options: this.businessTypeDict,
              rules: [
                { required: true, message: '请选择业务类型', trigger: 'change' }
              ]
            }
          },
          value10: {
            type: 'text',
            label: '使用次数',
            width: 100,
            search: { hidden: true },
            form: {
              hidden: true,
              type: 'input-number',
              placeholder: '使用次数',
              props: {
                min: 0,
                disabled: true
              }
            }
          },
          value11: {
            type: 'text',
            label: '创建人',
            width: 120,
            search: { hidden: true },
            form: {
              hidden: true,
              type: 'input',
              placeholder: '请输入创建人',
              rules: [
                { required: true, message: '创建人不能为空', trigger: 'blur' }
              ]
            }
          }

        }
      }
    },
    tableMixin() {
      return {
        ...(this.sheetRef?.tableMixin || {})
      }
    },
    pagingMixin() {
      return this.sheetRef?.pagingMixin || {}
    }
  },
  async mounted() {
    await this.$nextTick()
    this.sheetRef = this.$refs.sheetRef
  },
  methods: {

    // 获取算法图标
    getAlgorithmIcon(type) {
      const iconMap = {
        'encode_convert': 'el-icon-refresh',
        'data_preprocess': 'el-icon-magic-stick',
        'deep_process': 'el-icon-cpu'
      }
      return iconMap[type] || 'el-icon-coin'
    },
    // 获取状态类型
    getStatusType(status) {
      return status === '启用' ? 'success' : 'info'
    },
    // 获取状态标签
    getStatusLabel(status) {
      const statusItem = this.algorithmStatusDict.find(item => item.value === status)
      return statusItem ? statusItem.label : status
    },
    // 获取类型标签
    getTypeLabel(type) {
      const typeItem = this.algorithmTypeDict.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },
    // 获取业务类型标签
    getBusinessTypeLabel(type) {
      const typeItem = this.businessTypeDict.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },
    // 卡片点击事件
    handleCardClick(item) {
      console.log('Card clicked:', item)
    },
    // 参数配置
    handleParams(item) {
      this.$refs.modelParamsDialog.open(item)
    },
    // 编辑算法
    handleEdit(item) {
      this.sheetRef.handleEdit(item)
    },
    // 切换状态
    async handleToggleStatus(item) {
      const newStatus = item.value6 === '启用' ? '禁用' : '启用'

      await this.$confirm(`确定要${newStatus}算法 "${item.value2}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await this.sheetProps.api.edit({
        ...item,
        value6: newStatus
      })
      // 这里应该调用API更新状态
      item.value6 = newStatus
      this.$message.success(`${newStatus}成功`)

      this.$refs.sheetRef.getTableData()
    },
    // 删除算法
    handleDelete(item) {
      this.sheetRef.handleRemove(item)
    },
    // 新增算法
    handleAdd() {
      this.sheetRef.handleAdd()
    },
    // 批量导入
    handleBatchImport() {
      this.$message.info('批量导入')
    },
    // 批量导出
    handleBatchExport() {
      this.$message.info('批量导出')
    },
    // 批量删除
    handleBatchDelete() {
      this.$message.info('批量删除')
    },

    // 参数配置成功回调
    handleParamsSuccess() {
      // 刷新列表以更新参数量
      this.$refs.sheetRef.getTableData()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep.hidden-sheet-table {
  .ele-table-layout {
    display: none !important;
  }
}

.algorithm-model-page {
  .card-container {
    padding: 0 0;
    min-height: 400px;
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
    gap: 20px;
    padding: 0;
  }

  .algorithm-card {
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .card-item {
      height: 100%;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
      }

      ::v-deep .el-card__body {
        padding: 16px;
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .algorithm-info {
      display: flex;
      align-items: flex-start;
      flex: 1;

      .algorithm-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;

        i {
          font-size: 24px;
          color: white;
        }
      }

      .algorithm-details {
        flex: 1;
        min-width: 0;

        .algorithm-name {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 4px 0;
          line-height: 1.4;
          word-break: break-word;
        }

        .algorithm-code {
          font-size: 13px;
          color: #909399;
          margin: 0;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
      }
    }

    .algorithm-status {
      flex-shrink: 0;
      margin-left: 12px;
    }
  }

  .card-content {
    margin-bottom: 16px;

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px 16px;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        // border-bottom: 1px solid #f5f7fa;

        &.info-item-full {
          grid-column: 1 / -1;
        }

        .info-label {
          font-size: 13px;
          color: #909399;
          font-weight: 500;
          white-space: nowrap;
        }

        .info-value {
          font-size: 13px;
          color: #606266;
          font-weight: 600;
          text-align: right;
          word-break: break-word;
          @apply truncate max-w-2/3;
        }
      }
    }
  }

  .card-actions {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    padding-top: 12px;
    border-top: 1px solid #f5f7fa;

    .el-button {
      font-size: 12px;

      &.danger-btn {
        color: #f56c6c;

        &:hover {
          color: #f56c6c;
        }
      }
    }
  }
}

// 响应式设计
.algorithm-model-page .card-grid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

@media (max-width: 768px) {
  .algorithm-model-page {
    .card-container {
      padding: 12px 0;
    }

    .card-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .algorithm-card .card-item ::v-deep .el-card__body {
      padding: 16px;
    }

    .card-header {
      .algorithm-info {
        .algorithm-icon {
          width: 40px;
          height: 40px;

          i {
            font-size: 20px;
          }
        }

        .algorithm-details {
          .algorithm-name {
            font-size: 15px;
          }

          .algorithm-code {
            font-size: 12px;
          }
        }
      }
    }

    .card-content .info-grid {
      grid-template-columns: 1fr;
      gap: 8px;

      .info-item {
        padding: 6px 0;

        .info-label,
        .info-value {
          font-size: 12px;
        }
      }
    }

    .card-actions {
      flex-wrap: wrap;
      gap: 6px;

      .el-button {
        padding: 4px 8px;
        font-size: 11px;
      }
    }
  }
}

@media (max-width: 480px) {
  .algorithm-model-page {
    .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .algorithm-status {
        margin-left: 0;
        align-self: flex-end;
      }
    }

    .card-actions {
      justify-content: center;

      .el-button {
        flex: 1;
        min-width: 60px;
      }
    }
  }
}
</style>
