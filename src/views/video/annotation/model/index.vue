<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <template #toolbar:after>
    </template>

    <template #table:value5:simple="{ row }">
      <el-tag :type="getCombinationTagType(row.value5)">
        {{ row.value5 }}
      </el-tag>
    </template>

    <template #table:value6:simple="{ row }">
      <el-tag :type="getStatusTagType(row.value6)">
        {{ row.value6 }}
      </el-tag>
    </template>

    <!-- 关联算子显示 -->
    <template #table:value8:simple="{ row }">
      <div class="operator-tags">
        <el-tag
          v-for="operator in getOperatorList(row.value8)"
          :key="operator"
          size="mini"
          type="primary"
          style="margin: 2px;"
        >
          {{ operator }}
        </el-tag>
        <span v-if="!getOperatorList(row.value8).length" class="text-muted">暂无算子</span>
      </div>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button type="text" size="mini" @click="handleCopy(row)">
        复制
      </el-button>
    </template>

    <!-- 算子类型下拉列表 -->
    <template #after>
    </template>

    <template #info:before></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { operatorCombinationType, operatorChainStatus, operatorTypes, businessType } from '@/dicts/video/index.js'

export default {
  name: 'OperatorChainModel',
  data() {
    return {
      tableType: 'model_operator_orchestration',
      showOperatorList: false,
      operatorTypes,
      businessTypeDict: businessType,
      availableModels: [], // 可选择的模型列表
      selectedOperators: [] // 当前选中的算子
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '算子链模型',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          id: {
            type: 'text',
            label: 'ID',
            width: 80,
            fixed: 'left',
            search: { hidden: true },
            form: { hidden: true },
            info: { hidden: true }
          },
          // 编排名称
          value1: {
            type: 'text',
            label: '编排名称',
            width: 200,
            search: {
              type: 'input',
              placeholder: '请输入编排名称'
            },
            form: {
              type: 'input',
              placeholder: '请输入编排名称',
              rules: [
                { required: true, message: '编排名称不能为空', trigger: 'blur' },
                { min: 2, max: 50, message: '编排名称长度必须介于 2 和 50 之间', trigger: 'blur' }
              ]
            }
          },
          // 关联模型ID
          value2: {
            type: 'text',
            label: '关联模型',
            width: 200,
            search: {
              type: 'select',
              placeholder: '请选择关联模型',
              clearable: true,
              options: this.availableModels
            },
            form: {
              type: 'select',
              placeholder: '请选择关联模型',
              options: this.availableModels,
              rules: [
                { required: true, message: '请选择关联模型', trigger: 'change' }
              ]
            }
          },
          // 关联模型名称（用于显示）
          value3: {
            type: 'text',
            label: '模型名称',
            width: 200,
            search: { hidden: true },
            form: { hidden: true }
          },
          // 功能描述
          value4: {
            type: 'text',
            label: '功能描述',
            width: 250,
            search: {
              type: 'input',
              placeholder: '请输入功能描述关键词'
            },
            form: {
              type: 'textarea',
              placeholder: '请输入功能描述',
              rules: [
                { required: true, message: '功能描述不能为空', trigger: 'blur' }
              ]
            }
          },
          // 组合类型
          value5: {
            type: 'text',
            label: '组合类型',
            width: 120,
            search: {
              type: 'select',
              placeholder: '请选择组合类型',
              clearable: true,
              options: operatorCombinationType
            },
            form: {
              type: 'select',
              placeholder: '请选择组合类型',
              options: operatorCombinationType,
              rules: [
                { required: true, message: '请选择组合类型', trigger: 'change' }
              ]
            }
          },
          // 状态
          value6: {
            type: 'text',
            label: '状态',
            width: 100,
            search: {
              type: 'select',
              placeholder: '请选择状态',
              clearable: true,
              options: operatorChainStatus
            },
            form: {
              type: 'select',
              placeholder: '请选择状态',
              options: operatorChainStatus,
              rules: [
                { required: true, message: '请选择状态', trigger: 'change' }
              ]
            }
          },
          // 创建人
          value7: {
            type: 'text',
            label: '创建人',
            width: 120,
            search: { hidden: true },
            form: {
              hidden: true,
              type: 'input',
              placeholder: '请输入创建人'
            }
          },
          // 关联算子（JSON存储）
          value8: {
            type: 'text',
            label: '关联算子',
            width: 300,
            search: { hidden: true },
            form: {
              type: 'select',
              placeholder: '请选择关联算子',
              props: {
                multiple: true,
                filterable: true,
                'collapse-tags': true
              },
              options: operatorTypes,
              rules: [
                { required: true, message: '请至少选择一个算子', trigger: 'change' }
              ]
            }
          },
          // 创建时间
          value9: {
            type: 'text',
            label: '创建时间',
            width: 160,
            search: { hidden: true },
            form: {
              hidden: true,
              type: 'date-picker',
              props: {
                type: 'datetime',
                placeholder: '请选择创建时间',
                format: 'yyyy-MM-dd HH:mm:ss',
                valueFormat: 'yyyy-MM-dd HH:mm:ss',
                disabled: true
              }
            }
          }
        }
      }
    }
  },
  async mounted() {
    await this.loadAvailableModels()
  },
  methods: {
    // 加载可用的模型列表（仅视频标注类型）
    async loadAvailableModels() {
      try {
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'ai_algorithm_model_management',
            value9: 'video_annotation', // 仅加载视频标注类型的模型
            pageSize: 100
          }
        })

        if (response.code === 200 && response.rows && response.rows.length > 0) {
          this.availableModels = response.rows.map(item => ({
            label: item.value2, // 模型名称
            value: item.id // 模型ID
          }))
        } else {
          this.availableModels = []
        }
      } catch (error) {
        console.error('加载模型列表失败:', error)
        this.$message.error('加载模型列表失败')
        this.availableModels = []
      }
    },

    // 获取算子列表（从JSON字符串解析）
    getOperatorList(operatorJson) {
      try {
        if (!operatorJson) return []
        const operators = JSON.parse(operatorJson)
        return Array.isArray(operators) ? operators : []
      } catch (error) {
        console.warn('解析算子列表失败:', error)
        return []
      }
    },

    // 获取算子组合标签类型
    getCombinationTagType(combination) {
      const typeMap = {
        '串联': 'primary',
        '并联': 'success'
      }
      return typeMap[combination] || 'info'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '启用': 'success',
        '禁用': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 复制操作
    handleCopy(row) {
      this.$modal.confirm(`确认要复制算子链"${row.value2}"吗？`).then(() => {
        // 这里可以调用复制接口
        console.log('复制算子链:', row)
        this.$modal.msgSuccess('复制成功')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    }

  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}

.operator-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 280px;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

.operator-list-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 600px;
}

.operator-card {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.operator-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.operator-tag {
  margin: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.operator-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
